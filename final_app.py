#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرشد - التطبيق النهائي
تطبيق نافذة GUI نظيف وجميل
"""

import tkinter as tk
from tkinter import messagebox, font
import sqlite3
import hashlib
from datetime import datetime

class MurshidApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_database()
        self.current_user = None
        self.setup_styles()
        self.show_login_page()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🛡️ مرشد - تطبيق الإبلاغ عن المخالفات")
        self.root.geometry("360x640")
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (360 // 2)
        y = (self.root.winfo_screenheight() // 2) - (640 // 2)
        self.root.geometry(f"360x640+{x}+{y}")
        
        # الألوان
        self.colors = {
            'primary': '#4A90E2',
            'success': '#7ED321',
            'error': '#D0021B',
            'warning': '#F5A623',
            'background': '#F8F9FA',
            'surface': '#FFFFFF',
            'text': '#2C3E50'
        }
        
        self.root.configure(bg=self.colors['background'])
    
    def setup_styles(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': font.Font(family="Arial", size=16, weight="bold"),
            'header': font.Font(family="Arial", size=12, weight="bold"),
            'body': font.Font(family="Arial", size=10),
            'button': font.Font(family="Arial", size=10, weight="bold")
        }
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء حساب الأدمن
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, full_name, is_admin)
            VALUES (?, ?, ?, ?)
        ''', ("<EMAIL>", admin_password, "مدير النظام", True))
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, email, password):
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        hashed_password = self.hash_password(password)
        cursor.execute('''
            SELECT id, email, full_name, is_admin 
            FROM users 
            WHERE email = ? AND password = ?
        ''', (email, hashed_password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'email': user[1],
                'full_name': user[2],
                'is_admin': bool(user[3])
            }
        return None
    
    def register_user(self, email, password, full_name, phone=None):
        try:
            conn = sqlite3.connect('app.db')
            cursor = conn.cursor()
            
            hashed_password = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (email, password, full_name, phone)
                VALUES (?, ?, ?, ?)
            ''', (email, hashed_password, full_name, phone))
            
            conn.commit()
            conn.close()
            return True, "تم التسجيل بنجاح!"
            
        except sqlite3.IntegrityError:
            return False, "هذا البريد مستخدم بالفعل!"
        except Exception as e:
            return False, f"خطأ: {str(e)}"
    
    def clear_window(self):
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def create_button(self, parent, text, command, color='primary', **kwargs):
        """إنشاء زر جميل"""
        bg_color = self.colors[color]
        
        btn = tk.Button(
            parent,
            text=text,
            command=command,
            bg=bg_color,
            fg='white',
            font=self.fonts['button'],
            relief=tk.FLAT,
            bd=0,
            cursor='hand2',
            **kwargs
        )
        return btn
    
    def show_login_page(self):
        """صفحة تسجيل الدخول"""
        self.clear_window()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=30)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="🛡️ مرشد",
            font=self.fonts['title'],
            bg=self.colors['background'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=(20, 10))
        
        subtitle_label = tk.Label(
            main_frame,
            text="تطبيق الإبلاغ عن المخالفات",
            font=self.fonts['body'],
            bg=self.colors['background'],
            fg=self.colors['text']
        )
        subtitle_label.pack(pady=(0, 30))
        
        # بطاقة تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief=tk.RAISED, bd=1)
        login_frame.pack(fill=tk.X, pady=20)
        
        # محتوى البطاقة
        content_frame = tk.Frame(login_frame, bg=self.colors['surface'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(
            content_frame,
            text="🔐 تسجيل الدخول",
            font=self.fonts['header'],
            bg=self.colors['surface'],
            fg=self.colors['text']
        )
        login_title.pack(pady=(0, 15))
        
        # البريد الإلكتروني
        tk.Label(
            content_frame,
            text="📧 البريد الإلكتروني:",
            font=self.fonts['body'],
            bg=self.colors['surface'],
            fg=self.colors['text']
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(
            content_frame,
            font=self.fonts['body'],
            width=30,
            relief=tk.SOLID,
            bd=1
        )
        self.email_entry.pack(fill=tk.X, pady=(0, 10), ipady=5)
        self.email_entry.insert(0, "<EMAIL>")
        
        # كلمة المرور
        tk.Label(
            content_frame,
            text="🔒 كلمة المرور:",
            font=self.fonts['body'],
            bg=self.colors['surface'],
            fg=self.colors['text']
        ).pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(
            content_frame,
            font=self.fonts['body'],
            width=30,
            show="*",
            relief=tk.SOLID,
            bd=1
        )
        self.password_entry.pack(fill=tk.X, pady=(0, 15), ipady=5)
        self.password_entry.insert(0, "admin123")
        
        # أزرار
        login_btn = self.create_button(
            content_frame,
            "🚀 دخول",
            self.login,
            'primary'
        )
        login_btn.pack(fill=tk.X, pady=(0, 8), ipady=8)
        
        register_btn = self.create_button(
            content_frame,
            "📝 تسجيل جديد",
            self.show_register_page,
            'success'
        )
        register_btn.pack(fill=tk.X, ipady=8)
        
        # معلومات
        info_text = """💡 معلومات تجريبية:
👑 حساب الأدمن: <EMAIL> / admin123
📝 أو أنشئ حساب جديد"""
        
        info_label = tk.Label(
            main_frame,
            text=info_text,
            font=font.Font(family="Arial", size=9),
            bg=self.colors['background'],
            fg='gray',
            justify=tk.CENTER
        )
        info_label.pack(pady=15)
    
    def login(self):
        """تسجيل الدخول"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not email or not password:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول!")
            return
        
        user = self.authenticate_user(email, password)
        if user:
            self.current_user = user
            messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
            self.show_main_page()
        else:
            messagebox.showerror("خطأ", "بيانات خاطئة!")
    
    def show_register_page(self):
        """صفحة التسجيل"""
        self.clear_window()
        
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="📝 تسجيل حساب جديد",
            font=self.fonts['title'],
            bg=self.colors['background'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=(10, 20))
        
        # بطاقة التسجيل
        register_frame = tk.Frame(main_frame, bg=self.colors['surface'], relief=tk.RAISED, bd=1)
        register_frame.pack(fill=tk.BOTH, expand=True)
        
        content_frame = tk.Frame(register_frame, bg=self.colors['surface'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # الحقول
        fields = [
            ("👤 الاسم الكامل:", "full_name"),
            ("📧 البريد الإلكتروني:", "email"),
            ("📱 رقم الهاتف:", "phone"),
            ("🔒 كلمة المرور:", "password"),
            ("🔒 تأكيد كلمة المرور:", "confirm_password")
        ]
        
        self.register_entries = {}
        
        for label_text, field_name in fields:
            tk.Label(
                content_frame,
                text=label_text,
                font=self.fonts['body'],
                bg=self.colors['surface'],
                fg=self.colors['text']
            ).pack(anchor=tk.W, pady=(5, 2))
            
            entry = tk.Entry(
                content_frame,
                font=self.fonts['body'],
                width=30,
                show="*" if "password" in field_name else "",
                relief=tk.SOLID,
                bd=1
            )
            entry.pack(fill=tk.X, pady=(0, 8), ipady=4)
            self.register_entries[field_name] = entry
        
        # أزرار
        register_btn = self.create_button(
            content_frame,
            "✅ تسجيل",
            self.register,
            'success'
        )
        register_btn.pack(fill=tk.X, pady=(10, 5), ipady=8)
        
        back_btn = self.create_button(
            content_frame,
            "🔙 رجوع",
            self.show_login_page,
            'warning'
        )
        back_btn.pack(fill=tk.X, ipady=8)
    
    def register(self):
        """تسجيل مستخدم جديد"""
        full_name = self.register_entries['full_name'].get().strip()
        email = self.register_entries['email'].get().strip()
        phone = self.register_entries['phone'].get().strip()
        password = self.register_entries['password'].get().strip()
        confirm_password = self.register_entries['confirm_password'].get().strip()
        
        if not all([full_name, email, password, confirm_password]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة!")
            return
        
        if len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل!")
            return
        
        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمات المرور غير متطابقة!")
            return
        
        success, message = self.register_user(email, password, full_name, phone)
        
        if success:
            messagebox.showinfo("نجح", message)
            self.show_login_page()
        else:
            messagebox.showerror("خطأ", message)
    
    def show_main_page(self):
        """الصفحة الرئيسية"""
        self.clear_window()
        
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # الشريط العلوي
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # معلومات المستخدم
        user_info = f"👋 مرحباً {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " 👑"
        
        user_label = tk.Label(
            header_frame,
            text=user_info,
            font=self.fonts['header'],
            bg=self.colors['primary'],
            fg='white'
        )
        user_label.pack(side=tk.LEFT, padx=15, pady=15)
        
        # زر الخروج
        logout_btn = self.create_button(
            header_frame,
            "🚪 خروج",
            self.logout,
            'error'
        )
        logout_btn.pack(side=tk.RIGHT, padx=15, pady=10)
        
        # المحتوى
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # القائمة
        menu_items = [
            ("🏠 الصفحة الرئيسية", self.show_home, 'primary'),
            ("📝 بلاغ جديد", self.show_new_report, 'success'),
            ("📋 بلاغاتي", self.show_reports, 'warning'),
            ("💰 المحفظة", self.show_wallet, 'primary'),
            ("👤 الملف الشخصي", self.show_profile, 'primary')
        ]
        
        if self.current_user['is_admin']:
            menu_items.insert(0, ("👑 لوحة التحكم", self.show_admin, 'error'))
        
        for text, command, color in menu_items:
            btn = self.create_button(
                content_frame,
                text,
                command,
                color
            )
            btn.pack(fill=tk.X, pady=5, ipady=12)
    
    def show_home(self):
        messagebox.showinfo("الرئيسية", "🏠 مرحباً بك في تطبيق مرشد!")
    
    def show_new_report(self):
        messagebox.showinfo("بلاغ جديد", "📝 هذه الميزة قيد التطوير")
    
    def show_reports(self):
        messagebox.showinfo("بلاغاتي", "📋 هذه الميزة قيد التطوير")
    
    def show_wallet(self):
        messagebox.showinfo("المحفظة", "💰 هذه الميزة قيد التطوير")
    
    def show_profile(self):
        info = f"""👤 الملف الشخصي

الاسم: {self.current_user['full_name']}
البريد: {self.current_user['email']}
النوع: {'مدير النظام' if self.current_user['is_admin'] else 'مستخدم عادي'}"""
        messagebox.showinfo("الملف الشخصي", info)
    
    def show_admin(self):
        if not self.current_user['is_admin']:
            messagebox.showerror("خطأ", "غير مصرح!")
            return
        
        info = """👑 لوحة تحكم المدير

📊 الإحصائيات:
👥 المستخدمين: 1,247
📋 البلاغات: 3,456
✅ المقبولة: 2,890
💰 المكافآت: ₺45,678"""
        messagebox.showinfo("لوحة التحكم", info)
    
    def logout(self):
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟"):
            self.current_user = None
            self.show_login_page()
    
    def run(self):
        self.root.mainloop()

def main():
    print("🚀 تشغيل تطبيق مرشد...")
    print("📱 حجم النافذة: 360x640")
    print("✨ تطبيق نظيف وبسيط")
    
    app = MurshidApp()
    app.run()

if __name__ == "__main__":
    main()
